#!/bin/bash
# Simple sh to build a .s file into an executable

# If no argument provided, find the latest .s file
if [ $# -eq 0 ]; then
    latest_file=$(ls -t *.s 2>/dev/null | head -n 1)
    
    if [ -z "$latest_file" ]; then
        echo "No .s files found in current directory"
        exit 1
    fi
    
    echo "No file specified, using latest .s file: $latest_file"
    # Extract the base name without .s extension
    base_name="${latest_file%.s}"
elif [ $# -eq 1 ]; then
    base_name="$1"
else
    echo "Usage: $0 [file_name_without_extension]"
    echo "If no file_name is provided, the most recently edited .s file will be used."
    exit 1
fi

echo "Assembling ${base_name}.s..."
as -o ${base_name}.o ${base_name}.s

echo "Linking ${base_name}.o to create ${base_name} executable..."
ld -o ${base_name} ${base_name}.o

echo "Creating generic 'exe' executable..."
ld -o exe ${base_name}.o

echo "Submitting executable to /challenge/run."
/challenge/run exe