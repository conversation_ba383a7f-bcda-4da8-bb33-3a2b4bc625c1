# SSH and SCP File Transfer Guide

## Connection Details
- **Host:** pwnnsylvania.com
- **Port:** 2220
- **User:** hacker
- **Key:** key (in current directory)
- **Network requirement:** Must be within Penn's network

## SSH Connection

### Basic SSH Connection
```bash
ssh -<NAME_EMAIL> -p 2220
```

### Using the provided script
```bash
./ssh_simple.sh
```

## SCP File Transfer Commands

### Basic Syntax
```bash
scp -i key -P 2220 [source] [destination]
```

**Important:** SCP uses `-P` (uppercase) for port, while SSH uses `-p` (lowercase).

### Upload Operations

#### Upload a single file to server
```bash
scp -i key -P 2220 localfile.txt <EMAIL>:/home/<USER>/
```

#### Upload multiple files
```bash
scp -i key -P 2220 file1.txt file2.txt <EMAIL>:/home/<USER>/
```

#### Upload an entire directory (recursive)
```bash
scp -i key -P 2220 -r local_directory/ <EMAIL>:/home/<USER>/
```

#### Upload to a specific remote directory
```bash
scp -i key -P 2220 solution.py <EMAIL>:/home/<USER>/assignments/
```

### Download Operations

#### Download a single file from server
```bash
scp -i key -P 2220 <EMAIL>:/home/<USER>/remotefile.txt ./
```

#### Download to a specific local directory
```bash
scp -i key -P 2220 <EMAIL>:/home/<USER>/flag.txt ./downloads/
```

#### Download an entire directory
```bash
scp -i key -P 2220 -r <EMAIL>:/home/<USER>/remote_directory ./
```

#### Download multiple files
```bash
scp -i key -P 2220 <EMAIL>:/home/<USER>/file1.txt <EMAIL>:/home/<USER>/file2.txt ./
```

## SFTP Interactive Transfer

### Start SFTP Session
```bash
sftp -i key -P 2220 <EMAIL>
```

### SFTP Commands (once connected)

#### File Transfer Commands
- `put localfile.txt` - Upload a file to current remote directory
- `put localfile.txt remotename.txt` - Upload and rename file
- `get remotefile.txt` - Download a file to current local directory
- `get remotefile.txt localname.txt` - Download and rename file

#### Directory Navigation
- `ls` - List remote files and directories
- `lls` - List local files and directories
- `cd remote_directory` - Change remote directory
- `lcd local_directory` - Change local directory
- `pwd` - Show current remote directory
- `lpwd` - Show current local directory

#### Directory Operations
- `mkdir new_directory` - Create remote directory
- `lmkdir new_directory` - Create local directory
- `rmdir directory` - Remove remote directory
- `put -r local_directory/` - Upload directory recursively
- `get -r remote_directory/` - Download directory recursively

#### Other Useful Commands
- `help` - Show available commands
- `exit` or `quit` - Close SFTP session

## Example Workflows

### Example 1: Upload assignment solution
```bash
# Upload your Python solution
scp -i key -P 2220 assignment1.py <EMAIL>:/home/<USER>/

# Or upload multiple related files
scp -i key -P 2220 assignment1.py data.txt readme.md <EMAIL>:/home/<USER>/
```

### Example 2: Download results
```bash
# Download flag or results
scp -i key -P 2220 <EMAIL>:/home/<USER>/flag.txt ./

# Download entire results directory
scp -i key -P 2220 -r <EMAIL>:/home/<USER>/results ./
```

### Example 3: Interactive file management with SFTP
```bash
# Start SFTP session
sftp -i key -P 2220 <EMAIL>

# Once connected, you can:
sftp> ls                          # See remote files
sftp> lls                         # See local files  
sftp> put script.py               # Upload script.py
sftp> get output.txt              # Download output.txt
sftp> cd assignments              # Change to assignments directory
sftp> put -r project_folder/      # Upload entire folder
sftp> exit                        # Close session
```

## Tips and Notes

1. **Port difference:** Remember SSH uses `-p 2220` while SCP/SFTP use `-P 2220` (uppercase P)
2. **Network requirement:** You must be connected to Penn's network
3. **Persistent directories:** The `/home/<USER>
4. **Path format:** Remote paths use the format `<EMAIL>:/path/to/file`
5. **Current directory shortcut:** Use `./` for current local directory
6. **Recursive transfers:** Use `-r` flag for directories with both SCP and SFTP
7. **Wildcards:** You can use wildcards like `*.txt` or `assignment*.py`

## Troubleshooting

- If connection times out, ensure you're on Penn's network
- If permission denied, check that your key file has correct permissions (600)
- If file not found, verify the remote path exists by connecting via SSH first
- Use verbose mode with `-v` flag for debugging: `scp -v -i key -P 2220 ...`
