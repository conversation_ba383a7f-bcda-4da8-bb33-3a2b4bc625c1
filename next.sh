#!/bin/bash

# Find the latest .s file by modification time
latest_file=$(ls -t *.s 2>/dev/null | head -n 1)

if [ -z "$latest_file" ]; then
    echo "No .s files found in current directory"
    exit 1
fi

echo "Latest file found: $latest_file"

# Extract the prefix and number from the filename
# Pattern: prefix-number.s (e.g., 2-13.s)
if [[ $latest_file =~ ^(.+)-([0-9]+)\.s$ ]]; then
    prefix="${BASH_REMATCH[1]}"
    number="${BASH_REMATCH[2]}"
    
    # Increment the number
    new_number=$((number + 1))
    new_file="${prefix}-${new_number}.s"
    
    # Copy the file
    cp "$latest_file" "$new_file"
    echo "Copied $latest_file -> $new_file"
else
    echo "Error: Filename '$latest_file' doesn't match expected pattern (prefix-number.s)"
    exit 1
fi
