#!/bin/bash

# If no argument provided, find the latest .s file
if [ $# -eq 0 ]; then
    latest_file=$(ls -t *.s 2>/dev/null | head -n 1)
    
    if [ -z "$latest_file" ]; then
        echo "No .s files found in current directory"
        exit 1
    fi
    
    echo "No file specified, using latest .s file: $latest_file"
    file_to_upload="$latest_file"
elif [ $# -eq 1 ]; then
    file_to_upload="$1"
else
    echo "Usage: $0 [file_name]"
    echo "If no file_name is provided, the most recently edited .s file will be used."
    exit 1
fi

# SCP the file to the remote server
scp "$file_to_upload" p:/home/<USER>/
