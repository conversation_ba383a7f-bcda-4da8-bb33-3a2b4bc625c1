.intel_syntax noprefix
; end with y = rbx
; x = rdi
; rax = [x]
; rbx = [x+4]
; rcx = [x+8]
; rdx = [x+12]
mov rax, [rdi]
mov rbx, [rdi+4]
mov rcx, [rdi+8]
mov rdx, [rdi+12]

; if [x] == 0x7f454c46
cmp rax, 0x7f454c46
; if not
jmp else_first
; if true calculate
; y = [x+4] + [x+8] + [x+12]
add rbx, rcx
add rbx, rdx
jmp done

else_first:
; if [x] == 0x00005A4D
cmp rax, 0x00005A4D
; if not
jmp else
; if true calculate
; y = [x+4] - [x+8] - [x+12]
sub rbx, rcx
sub rbx, rdx
jmp done

else:
; y = [x+4] * [x+8] * [x+12]
mul rbx, rcx
mul rbx, rdx
jmp done

done:
; set y to the value of rbx
move rdi, rbx