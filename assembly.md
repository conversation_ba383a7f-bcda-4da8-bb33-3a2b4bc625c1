# Assembly Security and Hacking Reference Guide

**Purpose:** This document serves as a comprehensive reference for all assembly language concepts, security techniques, and hacking methodologies learned in class. It is designed for use during open book examinations and practical security assessments.

**Note:** This is a living document that will be continuously updated with class materials, examples, and practical techniques throughout the course.

---

## 🚨 Critical Homework Insights

### Key Takeaways from Student Q&A and Staff Responses

**1. Pwntools Payload Construction:**
- **DON'T use `pack()`** for challenges - it's incorrect
- **DO use specific byte packers:**
  - `p8(x)` → pack into 1 byte
  - `p16(x)` → pack into 2 bytes
  - `p32(x)` → pack into 4 bytes
  - `p64(x)` → pack into 8 bytes (used extensively in Project 3)
- **Payload format:** `payload = p8(...) + p16(...) + p32(...) + p64(...)`

**2. GDB Debugging Workflow:**
- **Local debugging first** - always test locally before remote
- **Breakpoint strategy** - set breakpoints AFTER input functions (like `fgets`)
- **Memory inspection** - use `x/10gx $rsp` to examine stack contents
- **Iterative approach** - modify payload, test, refine, repeat

**3. Binary Exploitation Process:**
1. **Prepare payload** in Python script and write to file
2. **Launch GDB** on the target binary
3. **Set breakpoints** after input functions
4. **Run with payload** using file redirection: `r < /home/<USER>/bytecode`
5. **Examine memory** to verify payload placement
6. **Iterate and refine** until payload works correctly

**4. Essential GDB Commands for Homework:**
- `disass main` - disassemble main function
- `break *main+63` - set breakpoint at specific offset
- `x/10gx $rsp` - examine 10 quad-words at stack pointer
- `r < filename` - run program with file input
- `info registers` - show all register values

**5. Project Expectations:**
- **Project 2:** Heavy GDB usage and debugging
- **Project 3:** Extensive use of `p64()` for 64-bit payloads
- **Debugging skills** are essential for success

---

## 📚 Table of Contents

1. [x64 System Call Reference](#x64-system-call-reference)
2. [Assembly Fundamentals](#assembly-fundamentals)
3. [Memory Management](#memory-management)
4. [Buffer Overflow Techniques](#buffer-overflow-techniques)
5. [Return-Oriented Programming (ROP)](#return-oriented-programming-rop)
6. [Shellcode Development](#shellcode-development)
7. [Exploitation Techniques](#exploitation-techniques)
8. [Defensive Mechanisms](#defensive-mechanisms)
9. [Debugging and Analysis Tools](#debugging-and-analysis-tools)
10. [Practical Examples](#practical-examples)

---

## 🔧 x64 System Call Reference

### Overview of x64.syscall.sh

**Website:** https://x64.syscall.sh/

This website provides a comprehensive reference for x64 (64-bit) Linux system calls, which are essential for:
- **Shellcode development** - Direct system interaction without library dependencies
- **Exploit development** - Low-level system manipulation
- **Reverse engineering** - Understanding program behavior at the kernel interface
- **Security analysis** - Identifying dangerous system call patterns

### Key System Call Information

The x64.syscall.sh reference provides:

#### System Call Table Structure
- **NR (Number):** Decimal and hexadecimal system call numbers
- **SYSCALL NAME:** Function name for the system call
- **RAX:** System call number (loaded into RAX register)
- **Arguments:** Up to 6 arguments passed in specific registers:
  - **ARG0 (RDI):** First argument
  - **ARG1 (RSI):** Second argument
  - **ARG2 (RDX):** Third argument
  - **ARG3 (R10):** Fourth argument
  - **ARG4 (R8):** Fifth argument
  - **ARG5 (R9):** Sixth argument

#### Critical System Calls for Security

**File Operations:**
- `read` (0): Read from file descriptor
- `write` (1): Write to file descriptor
- `open` (2): Open file
- `close` (3): Close file descriptor

**Process Control:**
- `execve` (59): Execute program - **Critical for shellcode**
- `fork` (57): Create child process
- `exit` (60): Terminate process
- `kill` (62): Send signal to process

**Memory Management:**
- `mmap` (9): Map memory - **Important for exploitation**
- `mprotect` (10): Change memory protection - **Bypass DEP/NX**
- `munmap` (11): Unmap memory

**Network Operations:**
- `socket` (41): Create socket - **Network shellcode**
- `connect` (42): Connect to remote host
- `bind` (49): Bind socket to address
- `listen` (50): Listen for connections

### x64 Calling Convention

**System Call Invocation:**
```assembly
mov rax, syscall_number    ; Load system call number
mov rdi, arg0             ; First argument
mov rsi, arg1             ; Second argument
mov rdx, arg2             ; Third argument
mov r10, arg3             ; Fourth argument (note: r10, not rcx)
mov r8, arg4              ; Fifth argument
mov r9, arg5              ; Sixth argument
syscall                   ; Invoke system call
```

**Important Notes:**
- Return value in RAX (negative values indicate errors)
- R10 is used instead of RCX for the 4th argument (unlike user-space calling convention)
- System calls can be interrupted and may return -EINTR

---

## 🏗 Assembly Fundamentals

### x64 Architecture Overview

**Registers:**
- **General Purpose:** RAX, RBX, RCX, RDX, RSI, RDI, RBP, RSP, R8-R15
- **Instruction Pointer:** RIP
- **Flags Register:** RFLAGS

**Register Sizes and Byte Access:**
- 64-bit: RAX, RBX, RCX, RDX, RSI, RDI, RBP, RSP, R8-R15
- 32-bit: EAX, EBX, ECX, EDX, ESI, EDI, EBP, ESP, R8D-R15D (lower 32 bits, zeros upper 32)
- 16-bit: AX, BX, CX, DX, SI, DI, BP, SP, R8W-R15W
- 8-bit: AL/AH, BL/BH, CL/CH, DL/DH, SIL, DIL, BPL, SPL, R8B-R15B

### Independent Register Byte Access

**Critical Concept:** You can access and modify specific portions of registers independently without affecting other parts.

**RAX Register Breakdown:**
```
MSB                                    LSB
+----------------------------------------+
|                   RAX (64-bit)         |
+--------------------+-------------------+
                     |    EAX (32-bit)   |
                     +---------+---------+
                               | AX (16) |
                               +----+----+
                               | AH | AL |
                               +----+----+
                               (8)  (8)
```

**Key Register Byte Names:**
- **AH** = Accumulator High (upper 8 bits of AX)
- **AL** = Accumulator Low (lower 8 bits of AX)
- **BH/BL** = Base register High/Low
- **CH/CL** = Counter register High/Low
- **DH/DL** = Data register High/Low

### Memory Layout

**Typical Process Memory Layout:**
```
High Memory (0x7FFFFFFFFFFF)
├── Kernel Space
├── Stack (grows down)
├── Memory Mapped Files
├── Heap (grows up)
├── BSS (uninitialized data)
├── Data (initialized data)
└── Text (code)
Low Memory (0x0000000000000000)
```

---

## 🧠 Memory Management

### Stack Operations

**Stack Frame Structure:**
```
Higher Addresses
├── Function Arguments
├── Return Address
├── Saved RBP (Base Pointer)
├── Local Variables
└── Temporary Data
Lower Addresses
```

**Key Instructions:**
- `push reg/imm` - Push onto stack, decrement RSP
- `pop reg` - Pop from stack, increment RSP
- `call addr` - Push RIP, jump to address
- `ret` - Pop into RIP, return to caller

---

## 📚 Stack Operations

### Understanding the Stack

**The Stack is a LIFO (Last In, First Out) data structure:**
- **Last value pushed** is the **first value popped**
- **Grows downward** in memory (toward lower addresses)
- **RSP (Stack Pointer)** always points to the top of the stack

### Stack Visualization

**Memory Layout:**
```
High Memory Addresses
         ↑
    +----------+
    |  Value3  | ← RSP points here (top of stack)
    +----------+
    |  Value2  |
    +----------+
    |  Value1  | ← First value pushed (bottom)
    +----------+
         ↓
Low Memory Addresses
```

**Kitchen Plate Analogy:**
```
Pushing plates:     Popping plates:
1. Push Red    →    1. Pop Blue     (last in, first out)
2. Push Green  →    2. Pop Green
3. Push Blue   →    3. Pop Red      (first in, last out)

Stack: Blue (top)   Stack: Empty
       Green
       Red (bottom)
```

### Stack Instructions

**Basic Operations:**
```assembly
push rax        ; Push RAX value onto stack (RSP decreases by 8)
pop rbx         ; Pop top value into RBX (RSP increases by 8)
push 0x1337     ; Push immediate value onto stack
```

**What Happens During Push/Pop:**
```assembly
; Before: RSP = 0x7fff1000, Stack = [Value1, Value2]
push rax        ; RSP = 0x7fff0ff8, Stack = [Value1, Value2, RAX_value]
                ; Stack grows DOWN (RSP decreases)

; Before: RSP = 0x7fff0ff8, Stack = [Value1, Value2, Value3]
pop rbx         ; RSP = 0x7fff1000, RBX = Value3, Stack = [Value1, Value2]
                ; Stack shrinks UP (RSP increases)
```

### Common Stack Patterns

**1. Temporary Storage:**
```assembly
push rax        ; Save RAX value
; ... do other operations that modify RAX ...
pop rax         ; Restore original RAX value
```

**2. Parameter Passing (older calling conventions):**
```assembly
push arg3       ; Push arguments in reverse order
push arg2
push arg1
call function   ; Function accesses arguments from stack
```

**3. Local Variables:**
```assembly
sub rsp, 16     ; Allocate 16 bytes of stack space
mov [rsp], rax  ; Store local variable
mov [rsp+8], rbx ; Store another local variable
; ... function body ...
add rsp, 16     ; Deallocate stack space
```

### Stack Manipulation Examples

**Example 1: Simple Value Modification**
```assembly
; Problem: Take top stack value, subtract RDI, put back
pop rax         ; Get top value
sub rax, rdi    ; Subtract RDI from it
push rax        ; Put modified value back
```

**Example 2: Swapping Top Two Values**
```assembly
pop rax         ; Get first value (was on top)
pop rbx         ; Get second value (was below first)
push rax        ; Push first value (now on bottom)
push rbx        ; Push second value (now on top)
```

**Example 3: Duplicating Top Value**
```assembly
pop rax         ; Get top value
push rax        ; Put it back
push rax        ; Push it again (now duplicated)
```

### Stack Security Implications

**1. Buffer Overflows:**
- Stack-based buffer overflows can overwrite return addresses
- Attackers can redirect program execution

**2. Return Address Protection:**
- Stack canaries detect stack corruption
- ASLR randomizes stack addresses

**3. ROP (Return-Oriented Programming):**
- Uses existing code snippets ending in `ret`
- Chains together by manipulating stack contents

### Stack Debugging

**GDB Commands:**
```bash
(gdb) info registers rsp    # Show stack pointer
(gdb) x/10gx $rsp          # Examine 10 quad-words from stack top
(gdb) bt                   # Show call stack (backtrace)
(gdb) frame                # Show current stack frame
```

**Common Issues:**
- **Stack imbalance:** Mismatched push/pop operations
- **Stack overflow:** Too many pushes without pops
- **Accessing uninitialized stack:** Reading before pushing

---

## 💥 Buffer Overflow Techniques

### Classic Stack Buffer Overflow

**Vulnerability Pattern:**
```c
void vulnerable_function(char *input) {
    char buffer[64];
    strcpy(buffer, input);  // No bounds checking!
}
```

**Exploitation Strategy:**
1. **Identify buffer size and offset to return address**
2. **Craft payload:** `[PADDING][RETURN_ADDRESS][SHELLCODE/ROP]`
3. **Control program execution flow**

### Heap Overflow Techniques

*[To be filled with class content]*

---

## 🔄 Return-Oriented Programming (ROP)

### ROP Fundamentals

**Concept:** Chain together existing code snippets ("gadgets") ending in `ret` instructions to perform arbitrary operations without injecting new code.

**Basic ROP Chain Structure:**
```
Stack Layout:
├── Gadget 1 Address
├── Gadget 2 Address
├── Gadget 3 Address
└── ...
```

*[To be expanded with class examples]*

---

## 🐚 Shellcode Development

### Basic Shellcode Principles

**Requirements:**
- **Position Independent:** Works regardless of memory location
- **Null-byte free:** Avoids string termination issues
- **Minimal size:** Fits within buffer constraints

### Example: execve("/bin/sh") Shellcode

```assembly
; execve("/bin/sh", NULL, NULL)
xor rax, rax        ; Clear RAX
push rax            ; NULL terminator
mov rbx, 0x68732f6e69622f  ; "/bin/sh" in reverse
push rbx            ; Push "/bin/sh" onto stack
mov rdi, rsp        ; RDI = pointer to "/bin/sh"
xor rsi, rsi        ; RSI = NULL (argv)
xor rdx, rdx        ; RDX = NULL (envp)
mov rax, 59         ; execve system call number
syscall             ; Execute
```

---

## 🧮 Arithmetic Operations and Register Manipulation

### Basic Arithmetic Instructions

**Addition and Subtraction:**
```assembly
add rax, rbx        ; rax = rax + rbx
sub rax, rbx        ; rax = rax - rbx
add rax, 0x10       ; rax = rax + 16 (immediate value)
sub rax, 0x10       ; rax = rax - 16
```

**Increment and Decrement:**
```assembly
inc rax             ; rax = rax + 1
dec rax             ; rax = rax - 1
```

### Multiplication Instructions

**Critical Difference: `mul` vs `imul`**

**`mul` (Unsigned Multiplication):**
- Uses implicit registers
- `mul reg` multiplies RAX by reg
- Result: High 64 bits in RDX, Low 64 bits in RAX
- **Cannot specify destination register**

```assembly
mov rax, 5          ; Load multiplicand
mov rbx, 3          ; Load multiplier
mul rbx             ; RAX * RBX, result in RDX:RAX
                    ; RDX = high 64 bits, RAX = low 64 bits
```

**`imul` (Signed Multiplication) - More Flexible:**
- Multiple forms available
- **Two-operand form:** `imul dst, src` (dst = dst * src)
- **Three-operand form:** `imul dst, src, imm` (dst = src * imm)
- **Can specify destination register**

```assembly
; Two-operand form
imul rax, rbx       ; rax = rax * rbx

; Three-operand form
imul rax, rbx, 5    ; rax = rbx * 5

; One-operand form (like mul)
imul rbx            ; RDX:RAX = RAX * RBX
```

### Linear Function Implementation: f(x) = mx + b

**Problem:** Compute f(x) = mx + b where:
- m = rdi (slope)
- x = rsi (input value)
- b = rdx (y-intercept)
- Result goes in rax

**Solution:**
```assembly
.intel_syntax noprefix

; Method 1: Using imul two-operand form
mov rax, rdi        ; rax = m
imul rax, rsi       ; rax = m * x
add rax, rdx        ; rax = mx + b

; Method 2: Using imul three-operand form (more efficient)
imul rax, rdi, rsi  ; rax = m * x (rdi * rsi)
add rax, rdx        ; rax = mx + b
```

**Why `imul` instead of `mul`?**
1. **Flexibility:** Can specify destination register
2. **Signed arithmetic:** Handles negative numbers correctly
3. **Multiple forms:** Three-operand form is very convenient
4. **No implicit register usage:** More predictable behavior

### Division Instructions

**`div` (Unsigned Division):**
```assembly
mov rax, 100        ; Dividend (low 64 bits)
xor rdx, rdx        ; Clear high 64 bits of dividend
mov rbx, 7          ; Divisor
div rbx             ; RAX = quotient, RDX = remainder
```

**`idiv` (Signed Division):**
```assembly
mov rax, -100       ; Dividend
cqo                 ; Sign-extend RAX into RDX:RAX
mov rbx, 7          ; Divisor
idiv rbx            ; RAX = quotient, RDX = remainder
```

### Register Usage Patterns

**Function Parameter Registers (System V ABI):**
- **RDI:** First parameter (m in our example)
- **RSI:** Second parameter (x in our example)
- **RDX:** Third parameter (b in our example)
- **RCX:** Fourth parameter
- **R8:** Fifth parameter
- **R9:** Sixth parameter

**Return Value:**
- **RAX:** Primary return value register

### Practical Examples

**Example 1: Simple Multiplication**
```assembly
; Multiply two registers and store result
mov rax, rdi        ; Load first operand
imul rax, rsi       ; Multiply by second operand
; Result now in rax
```

**Example 2: Complex Formula - Quadratic**
```assembly
; f(x) = ax² + bx + c
; a = rdi, x = rsi, b = rdx, c = rcx
mov rax, rsi        ; rax = x
imul rax, rax       ; rax = x²
imul rax, rdi       ; rax = ax²
mov r8, rsi         ; r8 = x
imul r8, rdx        ; r8 = bx
add rax, r8         ; rax = ax² + bx
add rax, rcx        ; rax = ax² + bx + c
```

**Example 3: Using Immediate Values**
```assembly
; Multiply by constant: result = input * 10 + 5
imul rax, rdi, 10   ; rax = rdi * 10
add rax, 5          ; rax = rdi * 10 + 5
```

### Common Pitfalls and Tips

**1. Register Clobbering:**
- `mul` and single-operand `imul` modify RDX
- Save important values before multiplication

**2. Overflow Handling:**
- Multiplication can overflow - check flags if needed
- Use 64-bit registers for intermediate calculations

**3. Signed vs Unsigned:**
- Use `imul` for signed arithmetic
- Use `mul` only when you need the full 128-bit result

**4. Efficiency:**
- Three-operand `imul` is often most efficient
- Avoid unnecessary `mov` instructions

**5. Challenge System Behavior:**
- Some systems check RAX after program execution
- Don't overwrite your result with exit codes
- Let the framework handle program termination

---

## 🗺️ Memory Layout and Instruction Encoding

### Memory Addresses in Assembly Output

When viewing disassembled code, the hex values on the left are **memory addresses** showing where each instruction is located:

```
0x400000:	mov   	rax, rdi     ; Instruction at address 0x400000
0x400003:	imul  	rax, rsi     ; Next instruction 3 bytes later
0x400007:	add   	rax, rdx     ; Next instruction 4 bytes later
0x40000a:	mov   	rdi, rax     ; Next instruction 3 bytes later
```

### Variable Instruction Lengths

**x86-64 instructions are variable length (1-15 bytes):**

| Instruction | Bytes | Reason |
|-------------|-------|---------|
| `mov rax, rdi` | 3 | Register-to-register move |
| `imul rax, rsi` | 4 | Two-operand multiplication |
| `add rax, rdx` | 3 | Register addition |
| `mov rax, 0x3c` | 7 | Includes 4-byte immediate value |
| `syscall` | 2 | Simple system call instruction |

### Memory Address Calculations

**Address progression example:**
- Start: 0x400000
- After 3-byte instruction: 0x400000 + 3 = 0x400003
- After 4-byte instruction: 0x400003 + 4 = 0x400007
- After 3-byte instruction: 0x400007 + 3 = 0x40000a

### Key Memory Concepts

**1. Instruction Pointer (RIP):**
- Points to the **next** instruction to execute
- Automatically incremented by instruction length
- Modified by jumps, calls, and returns

**2. Common Code Addresses:**
- **0x400000** - Typical starting address for executable code
- **0x401000** - Alternative common starting address
- Addresses are virtual (mapped by OS)

**3. Security Implications:**
- **Buffer overflows** can overwrite return addresses
- **ROP chains** target specific instruction addresses
- **ASLR** randomizes these addresses for security

### Debugging with Memory Addresses

**Using GDB to examine instructions:**
```bash
(gdb) disas /r main          # Show raw bytes with disassembly
(gdb) x/10i 0x400000        # Examine 10 instructions at address
(gdb) info registers rip     # Show current instruction pointer
```

**Understanding crash dumps:**
- **RIP value** shows where execution failed
- **Invalid instruction** often means RIP points to data, not code
- **Segmentation fault** usually means invalid memory access

### Practical Applications

**1. Exploit Development:**
- Calculate exact offsets for buffer overflows
- Find gadget addresses for ROP chains
- Understand memory layout for shellcode placement

**2. Reverse Engineering:**
- Map out function boundaries
- Identify code vs data sections
- Trace execution flow through addresses

**3. Debugging:**
- Set breakpoints at specific addresses
- Understand where crashes occur
- Analyze instruction-level execution

*[To be expanded with more examples from class]*

---

## 🛡 Defensive Mechanisms

### Modern Protection Mechanisms

**ASLR (Address Space Layout Randomization):**
- Randomizes memory layout
- **Bypass techniques:** Information leaks, brute force, partial overwrites

**DEP/NX (Data Execution Prevention):**
- Marks data pages as non-executable
- **Bypass techniques:** ROP, JOP, ret2libc

**Stack Canaries:**
- Random values placed before return address
- **Bypass techniques:** Canary leaks, partial overwrites

**FORTIFY_SOURCE:**
- Compile-time and runtime buffer overflow detection
- **Bypass techniques:** Heap overflows, format string bugs

*[To be expanded with class content]*

---

## 🔍 Debugging and Analysis Tools

### Essential Tools

**GDB (GNU Debugger):**
- `info registers` - Show register values
- `x/20x $rsp` - Examine stack memory
- `disas function` - Disassemble function

**Pwntools (Python):**
```python
from pwn import *
context(arch="amd64", os="linux", log_level="debug")
p = process('./vulnerable_binary')
payload = b'A' * 72 + p64(0xdeadbeef)
p.sendline(payload)
```

### Pwntools and GDB Integration

**Essential Pwntools Functions:**
```python
# Context setup
context(arch="amd64", os="linux", log_level="debug")

# Payload construction (CORRECT way)
payload = p8(0x41)           # Pack 1 byte
payload += p16(0x4142)       # Pack 2 bytes
payload += p32(0x41424344)   # Pack 4 bytes
payload += p64(0x4142434445464748)  # Pack 8 bytes

# Process interaction
p = process('/challenge/binary')
p.sendlineafter(b"prompt: ", payload)
flag = p.recvline()

# GDB attachment (for local debugging)
gdb.attach(p, 'continue')
```

**GDB Debugging Workflow with Pwntools:**

**Step 1: Prepare Payload File**
```python
from pwn import *
context(arch="amd64", os="linux", log_level="debug")

# Construct your payload
payload = p64(0xdeadbeef)  # Example payload

# Write to file for GDB testing
with open("/home/<USER>/bytecode", "wb") as f:
    f.write(payload)
```

**Step 2: GDB Analysis**
```bash
# Start GDB on target binary
gdb /challenge/binary

# Disassemble main to find key functions
(gdb) disass main

# Set breakpoint AFTER input function (e.g., fgets)
(gdb) break *main+63    # Offset after fgets call

# Run with payload file
(gdb) r < /home/<USER>/bytecode

# Examine stack after input
(gdb) x/10gx $rsp       # Show 10 quad-words from stack pointer
(gdb) info registers    # Show all register values
```

**Step 3: Payload Verification**
```bash
# Check if payload is correctly placed
(gdb) x/10gx $rsp
0x7ffe23efae50: 0x00000000deadbeef  # Your payload should appear here

# Examine specific memory regions
(gdb) x/s $rdi          # If RDI points to a string
(gdb) x/10i $rip        # Show next 10 instructions
```

**Step 4: Iterate and Refine**
1. Modify payload in Python script
2. Regenerate bytecode file
3. Rerun in GDB with `r < /home/<USER>/bytecode`
4. Verify payload placement and effects
5. Once working locally, use with pwntools remotely

**Common Pwntools Patterns:**
```python
# Remote connection
p = remote('hostname', port)

# Local process
p = process('./binary')

# Send payload after specific prompt
p.sendlineafter(b"Enter input: ", payload)

# Receive until specific string
response = p.recvuntil(b"Flag: ")

# Interactive mode (for manual interaction)
p.interactive()
```

*[To be expanded with tool usage from class]*

---

## 📝 Practical Examples

### Example 1: Linear Function Implementation
**Problem:** Compute f(x) = mx + b where m=rdi, x=rsi, b=rdx, result in rax

**Solution:**
```assembly
.intel_syntax noprefix
mov rax, rdi        ; Load m into rax
imul rax, rsi       ; rax = m * x
add rax, rdx        ; rax = mx + b
```

**Key Learning:** Don't overwrite your result with exit codes if the system checks RAX after execution.

### Example 2: Register Value Setting
**Problem:** Set multiple registers to specific values

**Solution:**
```assembly
.intel_syntax noprefix
mov rax, 0x1337
mov r12, 0xCAFED00D1337BEEF
mov rsp, 0x31337
```

**Key Learning:** Different registers serve different purposes (RAX=accumulator, RSP=stack pointer, R12=general purpose).

### Example 3: Program Flow Control
**Understanding when to exit vs when to let system handle termination:**

**Wrong approach (overwrites result):**
```assembly
mov rax, rdi
imul rax, rsi
add rax, rdx        ; Result in rax
mov rdi, rax        ; Save result
mov rax, 60         ; Overwrite result with exit code!
syscall
```

**Correct approach (preserves result):**
```assembly
mov rax, rdi
imul rax, rsi
add rax, rdx        ; Result stays in rax for system to check
```

### Example 4: Memory Address Analysis
**Understanding instruction layout:**
```
Address    | Instruction      | Bytes | Explanation
-----------|------------------|-------|---------------------------
0x400000   | mov rax, rdi     | 3     | Register-to-register move
0x400003   | imul rax, rsi    | 4     | Two-operand multiplication
0x400007   | add rax, rdx     | 3     | Register addition
0x40000a   | syscall          | 2     | System call instruction
```

**Key Learning:** Instructions have variable lengths, and addresses increment accordingly.

### Example 5: Register Byte Manipulation
**Problem:** Set upper 8 bits of ax register to 0x42 using one instruction

**Solution:**
```assembly
mov ah, 0x42        ; Modifies only AH (upper 8 bits of AX)
```

**Before and After:**
```
Before: RAX = 0x123456789ABCDEF0
        AX  = 0xDEF0 (AH=0xDE, AL=0xF0)

After:  RAX = 0x123456789ABC42F0  ; Only AH changed!
        AX  = 0x42F0 (AH=0x42, AL=0xF0)
```

**Key Learning:** You can surgically modify specific byte portions of registers without affecting other parts.

### Example 6: Memory Size Access
**Problem:** Load different sized data from same memory address into different registers

**Given:** Memory at 0x404000 contains 0x1234567890ABCDEF

**Solution:**
```assembly
mov al, [0x404000]     ; RAX gets byte: AL = 0xEF
mov bx, [0x404000]     ; RBX gets word: BX = 0xCDEF
mov ecx, [0x404000]    ; RCX gets dword: ECX = 0x90ABCDEF (clears upper 32 bits)
mov rdx, [0x404000]    ; RDX gets qword: RDX = 0x1234567890ABCDEF
```

**Key Learning:** Same memory address can be read in different sizes, affecting how much data is loaded.

### Example 7: Little Endian Memory Storage and 64-bit Immediate Limitations
**Problem:** Store 64-bit values in memory using indirect addressing

**Concept - Little Endian Byte Order:**
```
Value: 0x00000000DEADC0DE
Memory storage (Little Endian):
[0x1330] = 0xDE (least significant byte first)
[0x1331] = 0xC0
[0x1332] = 0xAD
[0x1333] = 0xDE
[0x1334] = 0x00
[0x1335] = 0x00
[0x1336] = 0x00
[0x1337] = 0x00 (most significant byte last)
```

**Problem:** Set [RDI] = 0xDEADBEEF00001337 and [RSI] = 0xC0FFEE0000

**Solution (64-bit values require register intermediate):**
```assembly
; Cannot do: mov [rdi], 0xDEADBEEF00001337  ; Too large for immediate!
; Must use register:
mov rax, 0xDEADBEEF00001337    ; Load 64-bit value into register
mov [rdi], rax                 ; Store register to memory

mov rbx, 0xC0FFEE0000          ; Load 40-bit value into register
mov [rsi], rbx                 ; Store register to memory
```

**Key Learning:** x86-64 mov instruction cannot directly store 64-bit immediate values to memory - must use register as intermediate.

### Example 8: Memory Offsets and Consecutive Data Access
**Problem:** Load two consecutive quad words, calculate sum, store result

**Concept - Linear Memory Layout:**
```
Memory Address | Data (Quad Word)
---------------|------------------
[0x1337]      | 0x00000000DEADBEEF  ← First quad word (8 bytes)
[0x1337 + 8]  | 0x00000000CAFEBABE  ← Second quad word (8 bytes later)
[0x1337 + 16] | 0x0000000012345678  ← Third quad word (16 bytes later)
```

**Byte-by-byte breakdown (Little Endian):**
```
[0x1337 + 0] = 0xEF  ← Least significant byte of first quad word
[0x1337 + 1] = 0xBE
[0x1337 + 2] = 0xAD
[0x1337 + 3] = 0xDE
[0x1337 + 4] = 0x00
[0x1337 + 5] = 0x00
[0x1337 + 6] = 0x00
[0x1337 + 7] = 0x00  ← Most significant byte of first quad word
[0x1337 + 8] = 0xBE  ← Start of second quad word
```

**Solution:**
```assembly
; Given: RDI contains base address, RSI contains result address
mov rax, [rdi]        ; Load first quad word from [RDI]
mov rbx, [rdi + 8]    ; Load second quad word from [RDI + 8]
add rax, rbx          ; Calculate sum: RAX = first + second
mov [rsi], rax        ; Store sum at address in RSI
```

**Key Learning:** To access consecutive quad words, add 8 bytes to the address (since 1 quad word = 8 bytes).

### Example 9: Stack Operations (Push/Pop)
**Problem:** Take top value from stack, subtract RDI from it, put it back

**Concept - Stack as LIFO (Last In, First Out):**
```
Stack Visualization (grows downward in memory):

High Memory
    |
    v
+----------+
|   Blue   | ← Top of stack (last pushed, first popped)
+----------+
|  Green   |
+----------+
|   Red    | ← Bottom (first pushed, last popped)
+----------+
    ^
    |
Low Memory
```

**Stack Instructions:**
- **`push reg`** - Put register value onto top of stack
- **`pop reg`** - Take top value from stack into register

**Stack Pointer (RSP):**
- Points to the **top** of the stack
- **Decrements** when you push (stack grows down)
- **Increments** when you pop (stack shrinks up)

**Solution Strategy:**
```assembly
; Step 1: Get the top value from stack
pop rax           ; RAX = top value from stack

; Step 2: Subtract RDI from it
sub rax, rdi      ; RAX = RAX - RDI

; Step 3: Put the result back on stack
push rax          ; Push modified value back onto stack
```

**Step-by-Step Execution:**
```
Initial State:
Stack: [Value1, Value2, Value3] ← Value3 is on top
RDI: 0x10

After pop rax:
RAX: Value3
Stack: [Value1, Value2] ← Value2 is now on top

After sub rax, rdi:
RAX: Value3 - 0x10
Stack: [Value1, Value2] ← unchanged

After push rax:
Stack: [Value1, Value2, (Value3-0x10)] ← modified value back on top
```

**Key Learning:** Stack operations follow LIFO principle - last pushed is first popped. Use pop to retrieve, modify in register, then push back.

*[To be expanded with more examples from class]*

---

## 🔧 Register Byte Manipulation

### Surgical Register Modification

**Key Concept:** x86-64 allows independent access to different portions of registers, enabling precise byte-level modifications without affecting other parts.

### Register Byte Access Patterns

**Complete Register Hierarchy:**

| 64-bit | 32-bit | 16-bit | 8-bit High | 8-bit Low |
|--------|--------|--------|------------|-----------|
| RAX    | EAX    | AX     | AH         | AL        |
| RBX    | EBX    | BX     | BH         | BL        |
| RCX    | ECX    | CX     | CH         | CL        |
| RDX    | EDX    | DX     | DH         | DL        |
| RSI    | ESI    | SI     | -          | SIL       |
| RDI    | EDI    | DI     | -          | DIL       |
| RBP    | EBP    | BP     | -          | BPL       |
| RSP    | ESP    | SP     | -          | SPL       |
| R8     | R8D    | R8W    | -          | R8B       |

**Note:** Only RAX, RBX, RCX, RDX have high byte registers (AH, BH, CH, DH).

### Practical Examples

**Example 1: Byte-Level Modifications**
```assembly
mov rax, 0x1234567890ABCDEF    ; Load full 64-bit value
mov al, 0xFF                   ; Change only lowest byte
mov ah, 0x00                   ; Change only second-lowest byte
; Result: RAX = 0x1234567890AB00FF
```

**Example 2: Register Size Interactions**
```assembly
mov rax, 0xFFFFFFFFFFFFFFFF    ; All bits set
mov eax, 0x12345678            ; Writing EAX zeros upper 32 bits!
; Result: RAX = 0x0000000012345678

mov ax, 0xABCD                 ; Writing AX preserves upper bits
; Result: RAX = 0x00000000123456CD

mov al, 0xEF                   ; Writing AL preserves other bits
; Result: RAX = 0x000000001234ABEF
```

### Security Applications

**1. Shellcode Optimization:**
```assembly
xor al, al              ; Clear AL (shorter than mov al, 0)
```

**2. Precise Memory Patching:**
```assembly
mov rax, [target_address]    ; Load 8-byte value
mov al, 0x90                 ; Change only lowest byte to NOP
mov [target_address], rax    ; Write back modified value
```

### Critical Pitfalls

**1. 32-bit Register Clearing:**
```assembly
mov rax, 0xFFFFFFFFFFFFFFFF
mov eax, 0x12345678          ; This CLEARS upper 32 bits!
; RAX becomes 0x0000000012345678, not 0xFFFFFFFF12345678
```

**2. High Byte Register Limitations:**
- AH, BH, CH, DH cannot be used with some modern instructions
- Not available for R8-R15 registers

---

## 🧠 Memory Operations and Dereferencing

### Understanding Memory Access

**Key Concept:** Memory is a linear address space from 0x0 to 0xFFFFFFFFFFFFFFFF in x86-64. Each address can store data that we can read from or write to.

### Dereferencing Syntax

**Square brackets `[]` mean "the value stored AT this address":**

```assembly
mov rax, [address]     ; Read: Get value stored AT address
mov [address], rax     ; Write: Store rax AT address
```

### Memory Access Patterns

**1. Direct Memory Access:**
```assembly
mov rax, [0x404000]    ; Read 8 bytes from address 0x404000 into RAX
mov [0x404000], rax    ; Write RAX to address 0x404000
```

**2. Indirect Memory Access (Register as Address):**
```assembly
mov rax, [rdi]         ; Read from address stored in RDI
mov [rdi], rax         ; Write RAX to address stored in RDI
```

**3. Register vs Memory Distinction:**
```assembly
mov rax, rdi           ; Copy RDI's value to RAX (register-to-register)
mov rax, [rdi]         ; Use RDI as address, read from that memory location
```

### Practical Examples

**Example 1: Basic Memory Read**
```assembly
; Problem: Read value stored at 0x404000 into RAX
mov rax, [0x404000]    ; Solution: Direct memory dereferencing
```

**Example 2: Indirect Memory Access**
```assembly
mov rdi, 0x404000      ; Load address into RDI
mov rax, [rdi]         ; Read from address in RDI (same as above)
```

**Example 3: Memory Write Operations**
```assembly
mov rax, 0x1337BEEF    ; Load value into RAX
mov [0x404000], rax    ; Write RAX to memory address 0x404000
```

**Example 4: Pointer Dereferencing**
```assembly
; If RDI contains 0x404000 and memory at 0x404000 contains 0xDEADBEEF
mov rax, rdi           ; RAX = 0x404000 (the address itself)
mov rbx, [rdi]         ; RBX = 0xDEADBEEF (value at that address)
```

### Memory Addressing Modes

**1. Immediate Address:**
```assembly
mov rax, [0x404000]    ; Direct address specified
```

**2. Register Indirect:**
```assembly
mov rax, [rdi]         ; Address stored in register
```

**3. Base + Displacement:**
```assembly
mov rax, [rdi + 8]     ; Address = RDI + 8 bytes
mov rax, [rdi - 4]     ; Address = RDI - 4 bytes
```

**4. Scaled Index (Advanced):**
```assembly
mov rax, [rdi + rsi*8] ; Address = RDI + (RSI * 8)
```

### Data Size Considerations

**Memory Size Terminology:**
- **Byte** = 1 byte = 8 bits
- **Word** = 2 bytes = 16 bits
- **Double Word (DWORD)** = 4 bytes = 32 bits
- **Quad Word (QWORD)** = 8 bytes = 64 bits

**Different instruction sizes read different amounts:**
```assembly
mov al, [0x404000]     ; Read 1 byte (byte)
mov ax, [0x404000]     ; Read 2 bytes (word)
mov eax, [0x404000]    ; Read 4 bytes (double word) - zeros upper 32 bits of RAX
mov rax, [0x404000]    ; Read 8 bytes (quad word)
```

**Memory Size Access Examples:**
```assembly
; Assuming memory at 0x404000 contains: 0x1234567890ABCDEF

mov al, [0x404000]     ; AL = 0xEF (least significant byte)
mov ax, [0x404000]     ; AX = 0xCDEF (least significant word)
mov eax, [0x404000]    ; EAX = 0x90ABCDEF (least significant dword)
mov rax, [0x404000]    ; RAX = 0x1234567890ABCDEF (full qword)
```

**Critical Behavior with EAX:**
```assembly
mov rax, 0xFFFFFFFFFFFFFFFF    ; RAX = all bits set
mov eax, [0x404000]            ; EAX clears upper 32 bits of RAX!
; RAX is now 0x00000000XXXXXXXX (where XXXXXXXX is the 32-bit value from memory)
```

### Security and Exploitation Applications

**1. Buffer Overflow Exploitation:**
```assembly
mov rax, [rsp + 8]     ; Read return address from stack
```

**2. Shellcode Memory Access:**
```assembly
mov rdi, [rip + data]  ; Position-independent data access
```

**3. Memory Corruption:**
```assembly
mov [target_addr], payload  ; Overwrite memory with malicious data
```

### Common Memory Regions

**Typical x86-64 Memory Layout:**
- **0x400000** - Code section (.text)
- **0x404000** - Data section (.data, .bss)
- **0x7fffff...** - Stack (high addresses, grows down)
- **Heap** - Dynamic allocation (grows up from low addresses)

### Critical Pitfalls

**1. Segmentation Faults:**
```assembly
mov rax, [0x0]         ; Accessing NULL pointer - will crash!
mov rax, [0xDEADBEEF]  ; Invalid address - will crash!
```

**2. Data Size Mismatches:**
```assembly
; Be careful about data alignment and size
mov al, [address]      ; Reads 1 byte
mov rax, [address]     ; Reads 8 bytes from same address
```

**3. Uninitialized Memory:**
```assembly
; Reading from uninitialized memory gives unpredictable results
mov rax, [some_address] ; Could contain garbage data
```

### Debugging Memory Operations

**GDB Commands for Memory:**
```bash
(gdb) x/8x 0x404000        # Examine 8 hex words at address
(gdb) x/s 0x404000         # Examine as string
(gdb) x/i 0x400000         # Examine as instruction
(gdb) info registers rdi   # Check register value used as address
```

---

## 🔢 Number Systems and Hexadecimal Notation

### Understanding the "0x" Prefix

**0x** is a prefix that indicates a number is written in **hexadecimal** (base 16) format.

**Common Number System Prefixes:**
- **0x** or **0X** = Hexadecimal (base 16)
- **0b** or **0B** = Binary (base 2)
- **0** (leading zero) = Octal (base 8) in some contexts
- **No prefix** = Decimal (base 10)

### Hexadecimal Digit Values

**Hexadecimal uses 16 symbols:**
```
Hex | Decimal | Binary
----|---------|--------
0   | 0       | 0000
1   | 1       | 0001
2   | 2       | 0010
3   | 3       | 0011
4   | 4       | 0100
5   | 5       | 0101
6   | 6       | 0110
7   | 7       | 0111
8   | 8       | 1000
9   | 9       | 1001
A   | 10      | 1010
B   | 11      | 1011
C   | 12      | 1100
D   | 13      | 1101
E   | 14      | 1110
F   | 15      | 1111
```

### Conversion Examples

**Hexadecimal to Decimal:**
- **0xDE** = D×16¹ + E×16⁰ = 13×16 + 14×1 = 208 + 14 = **222 decimal**
- **0xC0** = C×16¹ + 0×16⁰ = 12×16 + 0×1 = 192 + 0 = **192 decimal**
- **0xAD** = A×16¹ + D×16⁰ = 10×16 + 13×1 = 160 + 13 = **173 decimal**
- **0xBEEF** = B×16³ + E×16² + E×16¹ + F×16⁰ = 11×4096 + 14×256 + 14×16 + 15×1 = **48879 decimal**

**Decimal to Hexadecimal:**
- **255 decimal** = 15×16 + 15 = **0xFF**
- **256 decimal** = 1×256 + 0×16 + 0×1 = **0x100**
- **1337 decimal** = 5×256 + 3×16 + 9×1 = **0x539**

### Why Hexadecimal in Assembly?

**1. Compact Representation:**
- 1 hex digit = 4 bits (1 nibble)
- 2 hex digits = 8 bits (1 byte)
- 8 hex digits = 32 bits (4 bytes)
- 16 hex digits = 64 bits (8 bytes)

**2. Easy Binary Conversion:**
```
0xDEADBEEF = 1101 1110 1010 1101 1011 1110 1110 1111 (binary)
Each hex digit directly maps to 4 binary digits
```

**3. Memory Address Representation:**
```
64-bit address: 0x00007FFFFFFFE000
32-bit address: 0x08048000
Byte value: 0xFF
```

### Practical Memory Example

**Value 0x00000000DEADC0DE stored in Little Endian:**
```
Memory Address | Hex Value | Decimal Value | Binary
---------------|-----------|---------------|--------
[0x1330]      | 0xDE      | 222          | 11011110
[0x1331]      | 0xC0      | 192          | 11000000
[0x1332]      | 0xAD      | 173          | 10101101
[0x1333]      | 0xDE      | 222          | 11011110
[0x1334]      | 0x00      | 0            | 00000000
[0x1335]      | 0x00      | 0            | 00000000
[0x1336]      | 0x00      | 0            | 00000000
[0x1337]      | 0x00      | 0            | 00000000
```

### Common Hexadecimal Values in Security

**Memorable Hex Values:**
- **0xDEADBEEF** = 3735928559 decimal (common debug value)
- **0xCAFEBABE** = 3405691582 decimal (Java class file magic number)
- **0x41414141** = "AAAA" in ASCII (common in buffer overflows)
- **0x90909090** = NOP sled (0x90 = NOP instruction)
- **0xFFFFFFFF** = 4294967295 decimal (maximum 32-bit unsigned value)

### Quick Conversion Tips

**Powers of 16:**
- 16¹ = 16 (0x10)
- 16² = 256 (0x100)
- 16³ = 4096 (0x1000)
- 16⁴ = 65536 (0x10000)

**Common Patterns:**
- **0xFF** = 255 (all bits set in 1 byte)
- **0xFFFF** = 65535 (all bits set in 2 bytes)
- **0xFFFFFFFF** = 4294967295 (all bits set in 4 bytes)

### Calculator and Tools

**Converting between number systems:**
```bash
# Using Python
>>> hex(222)        # Decimal to hex
'0xde'
>>> int('0xde', 16) # Hex to decimal
222
>>> bin(222)        # Decimal to binary
'0b11011110'

# Using calculator (programmer mode)
# Most scientific calculators have HEX/DEC/BIN modes
```

---

## 🔄 Little Endian Byte Ordering

### Understanding Little Endian

**Key Concept:** x86-64 uses "Little Endian" byte ordering, where the least significant byte is stored at the lowest memory address.

### Byte Order Visualization

**Big Endian (NOT x86-64):**
```
Value: 0x12345678
[addr+0] = 0x12  (most significant byte first)
[addr+1] = 0x34
[addr+2] = 0x56
[addr+3] = 0x78  (least significant byte last)
```

**Little Endian (x86-64):**
```
Value: 0x12345678
[addr+0] = 0x78  (least significant byte first)
[addr+1] = 0x56
[addr+2] = 0x34
[addr+3] = 0x12  (most significant byte last)
```

### Practical Example

**64-bit Value Storage:**
```
Value: 0x00000000DEADC0DE
Memory Layout in Little Endian:
[0x1330] = 0xDE  ← Least significant byte
[0x1331] = 0xC0
[0x1332] = 0xAD
[0x1333] = 0xDE
[0x1334] = 0x00
[0x1335] = 0x00
[0x1336] = 0x00
[0x1337] = 0x00  ← Most significant byte
```

### Why Little Endian Matters

**1. Memory Analysis:**
- When examining memory dumps, bytes appear "reversed"
- Understanding byte order is crucial for debugging

**2. Exploitation:**
- Buffer overflows depend on byte ordering
- Return address overwrites must account for endianness

**3. Data Interpretation:**
- Multi-byte values read differently than expected
- Important for reverse engineering

## 🚫 x86-64 Instruction Limitations

### 64-bit Immediate Value Restrictions

**The Problem:** x86-64 mov instruction cannot directly store 64-bit immediate values to memory.

**What DOESN'T Work:**
```assembly
mov [rdi], 0xDEADBEEF00001337    ; ERROR: Immediate too large!
mov [0x404000], 0x123456789ABCDEF ; ERROR: 64-bit immediate not allowed!
```

**What WORKS:**
```assembly
; Method 1: Use register as intermediate
mov rax, 0xDEADBEEF00001337      ; Load into register first
mov [rdi], rax                   ; Then store to memory

; Method 2: Use smaller immediate values (32-bit max)
mov dword ptr [rdi], 0x00001337      ; Store lower 32 bits
mov dword ptr [rdi+4], 0xDEADBEEF    ; Store upper 32 bits
```

### Immediate Value Size Limits

**Maximum immediate sizes by instruction:**
- **8-bit operations:** 8-bit immediate (0xFF max)
- **16-bit operations:** 16-bit immediate (0xFFFF max)
- **32-bit operations:** 32-bit immediate (0xFFFFFFFF max)
- **64-bit operations:** 32-bit immediate (sign-extended to 64-bit)

**Examples:**
```assembly
mov al, 0xFF                    ; ✅ 8-bit immediate
mov ax, 0x1234                  ; ✅ 16-bit immediate
mov eax, 0x12345678             ; ✅ 32-bit immediate
mov rax, 0x12345678             ; ✅ 32-bit immediate (sign-extended)
mov rax, 0x123456789ABCDEF0     ; ❌ 64-bit immediate not allowed!
```

### Workarounds for Large Constants

**Method 1: Register Loading**
```assembly
mov rax, 0x123456789ABCDEF0     ; Load large constant
mov [memory_addr], rax          ; Store to memory
```

**Method 2: Split into 32-bit Parts**
```assembly
mov dword ptr [rdi], 0x9ABCDEF0     ; Lower 32 bits
mov dword ptr [rdi+4], 0x12345678   ; Upper 32 bits
```

**Method 3: Use movabs for Register Loading**
```assembly
movabs rax, 0x123456789ABCDEF0  ; Special instruction for 64-bit immediate to register
mov [rdi], rax                  ; Then store to memory
```

### Security Implications

**1. Shellcode Constraints:**
- Large addresses require register loading
- Affects shellcode size and complexity

**2. ROP Gadget Limitations:**
- Cannot directly load 64-bit constants
- Must find gadgets that load from memory or registers

**3. Exploitation Techniques:**
- Return address overwrites limited by immediate size
- May require multiple writes or register manipulation

---

## 📖 Quick Reference

### Common x64 Instructions

**Data Movement:**
- `mov dst, src` - Move data

**Arithmetic:**
- `add dst, src` - Addition
- `sub dst, src` - Subtraction
- `inc reg` - Increment by 1
- `dec reg` - Decrement by 1
- `imul dst, src` - Signed multiplication (two-operand)
- `imul dst, src, imm` - Signed multiplication (three-operand)
- `mul src` - Unsigned multiplication (result in RDX:RAX)
- `div src` - Unsigned division (RAX = quotient, RDX = remainder)
- `idiv src` - Signed division (RAX = quotient, RDX = remainder)

**Comparison and Control Flow:**
- `cmp op1, op2` - Compare (sets flags)
- `jmp addr` - Unconditional jump
- `je/jz addr` - Jump if equal/zero
- `jne/jnz addr` - Jump if not equal/not zero

### Register Quick Reference

**General Purpose Registers and Their Conventional Uses:**

- **RAX:** Accumulator, system call number, return value
- **RBX:** Base register, general purpose, callee-saved
- **RCX:** Counter register, fourth function argument, loop counter
- **RDX:** Data register, third function argument, used with RAX for large operations
- **RSI:** Source Index, second function argument, source pointer for string operations
- **RDI:** Destination Index, first function argument, destination pointer for string operations
- **RBP:** Base Pointer (frame pointer), points to current stack frame
- **RSP:** Stack Pointer, points to top of stack
- **R8-R15:** Extended general-purpose registers (64-bit mode additions)

**Function Parameter Registers (System V ABI):**
1. **RDI** - First parameter
2. **RSI** - Second parameter
3. **RDX** - Third parameter
4. **RCX** - Fourth parameter
5. **R8** - Fifth parameter
6. **R9** - Sixth parameter
7. **Stack** - Additional parameters

### System Call Quick Reference (Most Common)

| Number | Name | Purpose | Arguments |
|--------|------|---------|-----------|
| 0 | read | Read from file | fd, buf, count |
| 1 | write | Write to file | fd, buf, count |
| 2 | open | Open file | filename, flags, mode |
| 3 | close | Close file | fd |
| 9 | mmap | Map memory | addr, length, prot, flags, fd, offset |
| 10 | mprotect | Change memory protection | start, len, prot |
| 11 | munmap | Unmap memory | addr, len |
| 41 | socket | Create socket | domain, type, protocol |
| 42 | connect | Connect socket | sockfd, addr, addrlen |
| 57 | fork | Create process | - |
| 59 | execve | Execute program | filename, argv, envp |
| 60 | exit | Exit process | error_code |

### Memory Protection Flags (mprotect)
- **PROT_READ (1):** Page can be read
- **PROT_WRITE (2):** Page can be written
- **PROT_EXEC (4):** Page can be executed
- **PROT_NONE (0):** Page cannot be accessed

---

## 🎯 Exploitation Cheat Sheet

### Buffer Overflow Pattern
1. **Find vulnerability** (strcpy, gets, scanf, etc.)
2. **Determine offset** to return address
3. **Identify bad characters** (null bytes, newlines, etc.)
4. **Choose exploitation method:**
   - Direct shellcode injection
   - Return-to-libc
   - ROP chain
   - ret2syscall

### ROP Gadget Types
- **Pop gadgets:** `pop rdi; ret` - Load values into registers
- **Arithmetic gadgets:** `add rax, rbx; ret` - Perform calculations
- **Memory gadgets:** `mov [rax], rbx; ret` - Write to memory
- **System call gadgets:** `syscall; ret` - Invoke system calls

### Shellcode Encoding Techniques
- **XOR encoding:** Avoid null bytes
- **Alpha-numeric encoding:** Only printable characters
- **Polymorphic encoding:** Change appearance while maintaining functionality

## 📍 Register Usage Patterns and Pointer Concepts

### RSI and RDI: Source and Destination Registers

**Historical Purpose:**
- **RSI** = "**R**egister **S**ource **I**ndex" - points to source data
- **RDI** = "**R**egister **D**estination **I**ndex" - points to destination

**String/Memory Operations:**
```assembly
; String copy operations use RSI (source) and RDI (destination)
movsb    ; Move byte from [RSI] to [RDI], increment both
movsw    ; Move word from [RSI] to [RDI], increment both
movsd    ; Move dword from [RSI] to [RDI], increment both
movsq    ; Move qword from [RSI] to [RDI], increment both

; Example: Copy 8 bytes from one location to another
mov rsi, source_address      ; RSI points to source
mov rdi, dest_address        ; RDI points to destination
movsq                        ; Copy 8 bytes from [RSI] to [RDI]
```

### Registers as Pointers

**Key Concept:** Registers can hold **addresses** (pointers) rather than data values.

**Example from Challenge:**
```assembly
; Challenge gives you addresses in registers:
; RDI = 0x404000 (address of first memory location)
; RSI = 0x405000 (address of second memory location)

; Task: Store values at these addresses
mov rax, 0xDEADBEEF00001337    ; Load 64-bit value
mov [rdi], rax                 ; Store at address contained in RDI

mov rbx, 0xC0FFEE0000          ; Load another value
mov [rsi], rbx                 ; Store at address contained in RSI
```

**Understanding the Distinction:**
```assembly
mov rdi, 0x404000      ; RDI = 0x404000 (RDI contains the address)
mov rax, rdi           ; RAX = 0x404000 (copy the address itself)
mov rax, [rdi]         ; RAX = value stored AT address 0x404000
```

### Function Calling Convention Usage

**System V ABI (Linux/Unix):**
```assembly
; Function call: my_function(arg1, arg2, arg3, arg4, arg5, arg6)
mov rdi, arg1          ; First parameter
mov rsi, arg2          ; Second parameter
mov rdx, arg3          ; Third parameter
mov rcx, arg4          ; Fourth parameter
mov r8, arg5           ; Fifth parameter
mov r9, arg6           ; Sixth parameter
call my_function       ; Call function
; Return value in RAX
```

**Practical Example:**
```assembly
; Call: write(fd=1, buffer=msg, count=13)
mov rdi, 1             ; fd = 1 (stdout)
mov rsi, msg           ; buffer = address of message
mov rdx, 13            ; count = 13 bytes
mov rax, 1             ; write system call number
syscall                ; Invoke system call
```

### Register Roles in Different Contexts

**Context 1: Function Parameters**
```assembly
; RDI and RSI hold parameter values
mov rdi, 42            ; First parameter = 42
mov rsi, 1337          ; Second parameter = 1337
call some_function
```

**Context 2: Memory Pointers**
```assembly
; RDI and RSI hold memory addresses
mov rdi, buffer1       ; RDI points to first buffer
mov rsi, buffer2       ; RSI points to second buffer
; Now use [rdi] and [rsi] to access the buffers
```

**Context 3: String Operations**
```assembly
; RDI = destination, RSI = source for string copy
mov rsi, source_string    ; Source address
mov rdi, dest_buffer      ; Destination address
mov rcx, string_length    ; Number of bytes to copy
rep movsb                 ; Repeat move byte operation
```

### Security Implications

**1. Buffer Overflow Targets:**
```assembly
; Attackers often target RSI/RDI when they point to buffers
mov rdi, user_buffer      ; RDI points to user-controlled buffer
; If buffer overflows, can overwrite adjacent memory
```

**2. ROP Gadget Patterns:**
```assembly
; Common ROP gadgets involving RSI/RDI
pop rdi; ret              ; Load address into RDI
pop rsi; ret              ; Load address into RSI
mov [rdi], rsi; ret       ; Write RSI value to RDI address
```

**3. Shellcode Techniques:**
```assembly
; Position-independent shellcode often uses RSI/RDI
lea rsi, [rip + data]     ; RSI points to data relative to current position
mov rdi, target_addr      ; RDI points to target location
```

---

**Last Updated:** [Date will be updated as content is added]
**Course:** CIS 5510 - Computer and Network Security
**Semester:** [To be filled]

---

## 📋 Notes Section

*[Use this space for handwritten notes during class, additional examples, and personal observations]*