# Assembly Security and Hacking Reference Guide

**Purpose:** This document serves as a comprehensive reference for all assembly language concepts, security techniques, and hacking methodologies learned in class. It is designed for use during open book examinations and practical security assessments.

**Note:** This is a living document that will be continuously updated with class materials, examples, and practical techniques throughout the course.

---

## 📚 Table of Contents

1. [x64 System Call Reference](#x64-system-call-reference)
2. [Assembly Fundamentals](#assembly-fundamentals)
3. [Memory Management](#memory-management)
4. [Buffer Overflow Techniques](#buffer-overflow-techniques)
5. [Return-Oriented Programming (ROP)](#return-oriented-programming-rop)
6. [Shellcode Development](#shellcode-development)
7. [Exploitation Techniques](#exploitation-techniques)
8. [Defensive Mechanisms](#defensive-mechanisms)
9. [Debugging and Analysis Tools](#debugging-and-analysis-tools)
10. [Practical Examples](#practical-examples)

---

## 🔧 x64 System Call Reference

### Overview of x64.syscall.sh

**Website:** https://x64.syscall.sh/

This website provides a comprehensive reference for x64 (64-bit) Linux system calls, which are essential for:
- **Shellcode development** - Direct system interaction without library dependencies
- **Exploit development** - Low-level system manipulation
- **Reverse engineering** - Understanding program behavior at the kernel interface
- **Security analysis** - Identifying dangerous system call patterns

### Key System Call Information

The x64.syscall.sh reference provides:

#### System Call Table Structure
- **NR (Number):** Decimal and hexadecimal system call numbers
- **SYSCALL NAME:** Function name for the system call
- **RAX:** System call number (loaded into RAX register)
- **Arguments:** Up to 6 arguments passed in specific registers:
  - **ARG0 (RDI):** First argument
  - **ARG1 (RSI):** Second argument
  - **ARG2 (RDX):** Third argument
  - **ARG3 (R10):** Fourth argument
  - **ARG4 (R8):** Fifth argument
  - **ARG5 (R9):** Sixth argument

#### Critical System Calls for Security

**File Operations:**
- `read` (0): Read from file descriptor
- `write` (1): Write to file descriptor
- `open` (2): Open file
- `close` (3): Close file descriptor

**Process Control:**
- `execve` (59): Execute program - **Critical for shellcode**
- `fork` (57): Create child process
- `exit` (60): Terminate process
- `kill` (62): Send signal to process

**Memory Management:**
- `mmap` (9): Map memory - **Important for exploitation**
- `mprotect` (10): Change memory protection - **Bypass DEP/NX**
- `munmap` (11): Unmap memory

**Network Operations:**
- `socket` (41): Create socket - **Network shellcode**
- `connect` (42): Connect to remote host
- `bind` (49): Bind socket to address
- `listen` (50): Listen for connections

### x64 Calling Convention

**System Call Invocation:**
```assembly
mov rax, syscall_number    ; Load system call number
mov rdi, arg0             ; First argument
mov rsi, arg1             ; Second argument
mov rdx, arg2             ; Third argument
mov r10, arg3             ; Fourth argument (note: r10, not rcx)
mov r8, arg4              ; Fifth argument
mov r9, arg5              ; Sixth argument
syscall                   ; Invoke system call
```

**Important Notes:**
- Return value in RAX (negative values indicate errors)
- R10 is used instead of RCX for the 4th argument (unlike user-space calling convention)
- System calls can be interrupted and may return -EINTR

---

## 🏗 Assembly Fundamentals

### x64 Architecture Overview

**Registers:**
- **General Purpose:** RAX, RBX, RCX, RDX, RSI, RDI, RBP, RSP, R8-R15
- **Instruction Pointer:** RIP
- **Flags Register:** RFLAGS

**Register Sizes:**
- 64-bit: RAX, RBX, etc.
- 32-bit: EAX, EBX, etc. (lower 32 bits, zeros upper 32)
- 16-bit: AX, BX, etc.
- 8-bit: AL/AH, BL/BH, etc.

### Memory Layout

**Typical Process Memory Layout:**
```
High Memory (0x7FFFFFFFFFFF)
├── Kernel Space
├── Stack (grows down)
├── Memory Mapped Files
├── Heap (grows up)
├── BSS (uninitialized data)
├── Data (initialized data)
└── Text (code)
Low Memory (0x0000000000000000)
```

---

## 🧠 Memory Management

### Stack Operations

**Stack Frame Structure:**
```
Higher Addresses
├── Function Arguments
├── Return Address
├── Saved RBP (Base Pointer)
├── Local Variables
└── Temporary Data
Lower Addresses
```

**Key Instructions:**
- `push reg/imm` - Push onto stack, decrement RSP
- `pop reg` - Pop from stack, increment RSP
- `call addr` - Push RIP, jump to address
- `ret` - Pop into RIP, return to caller

---

## 💥 Buffer Overflow Techniques

### Classic Stack Buffer Overflow

**Vulnerability Pattern:**
```c
void vulnerable_function(char *input) {
    char buffer[64];
    strcpy(buffer, input);  // No bounds checking!
}
```

**Exploitation Strategy:**
1. **Identify buffer size and offset to return address**
2. **Craft payload:** `[PADDING][RETURN_ADDRESS][SHELLCODE/ROP]`
3. **Control program execution flow**

### Heap Overflow Techniques

*[To be filled with class content]*

---

## 🔄 Return-Oriented Programming (ROP)

### ROP Fundamentals

**Concept:** Chain together existing code snippets ("gadgets") ending in `ret` instructions to perform arbitrary operations without injecting new code.

**Basic ROP Chain Structure:**
```
Stack Layout:
├── Gadget 1 Address
├── Gadget 2 Address
├── Gadget 3 Address
└── ...
```

*[To be expanded with class examples]*

---

## 🐚 Shellcode Development

### Basic Shellcode Principles

**Requirements:**
- **Position Independent:** Works regardless of memory location
- **Null-byte free:** Avoids string termination issues
- **Minimal size:** Fits within buffer constraints

### Example: execve("/bin/sh") Shellcode

```assembly
; execve("/bin/sh", NULL, NULL)
xor rax, rax        ; Clear RAX
push rax            ; NULL terminator
mov rbx, 0x68732f6e69622f  ; "/bin/sh" in reverse
push rbx            ; Push "/bin/sh" onto stack
mov rdi, rsp        ; RDI = pointer to "/bin/sh"
xor rsi, rsi        ; RSI = NULL (argv)
xor rdx, rdx        ; RDX = NULL (envp)
mov rax, 59         ; execve system call number
syscall             ; Execute
```

---

## 🧮 Arithmetic Operations and Register Manipulation

### Basic Arithmetic Instructions

**Addition and Subtraction:**
```assembly
add rax, rbx        ; rax = rax + rbx
sub rax, rbx        ; rax = rax - rbx
add rax, 0x10       ; rax = rax + 16 (immediate value)
sub rax, 0x10       ; rax = rax - 16
```

**Increment and Decrement:**
```assembly
inc rax             ; rax = rax + 1
dec rax             ; rax = rax - 1
```

### Multiplication Instructions

**Critical Difference: `mul` vs `imul`**

**`mul` (Unsigned Multiplication):**
- Uses implicit registers
- `mul reg` multiplies RAX by reg
- Result: High 64 bits in RDX, Low 64 bits in RAX
- **Cannot specify destination register**

```assembly
mov rax, 5          ; Load multiplicand
mov rbx, 3          ; Load multiplier
mul rbx             ; RAX * RBX, result in RDX:RAX
                    ; RDX = high 64 bits, RAX = low 64 bits
```

**`imul` (Signed Multiplication) - More Flexible:**
- Multiple forms available
- **Two-operand form:** `imul dst, src` (dst = dst * src)
- **Three-operand form:** `imul dst, src, imm` (dst = src * imm)
- **Can specify destination register**

```assembly
; Two-operand form
imul rax, rbx       ; rax = rax * rbx

; Three-operand form
imul rax, rbx, 5    ; rax = rbx * 5

; One-operand form (like mul)
imul rbx            ; RDX:RAX = RAX * RBX
```

### Linear Function Implementation: f(x) = mx + b

**Problem:** Compute f(x) = mx + b where:
- m = rdi (slope)
- x = rsi (input value)
- b = rdx (y-intercept)
- Result goes in rax

**Solution:**
```assembly
.intel_syntax noprefix

; Method 1: Using imul two-operand form
mov rax, rdi        ; rax = m
imul rax, rsi       ; rax = m * x
add rax, rdx        ; rax = mx + b

; Method 2: Using imul three-operand form (more efficient)
imul rax, rdi, rsi  ; rax = m * x (rdi * rsi)
add rax, rdx        ; rax = mx + b
```

**Why `imul` instead of `mul`?**
1. **Flexibility:** Can specify destination register
2. **Signed arithmetic:** Handles negative numbers correctly
3. **Multiple forms:** Three-operand form is very convenient
4. **No implicit register usage:** More predictable behavior

### Division Instructions

**`div` (Unsigned Division):**
```assembly
mov rax, 100        ; Dividend (low 64 bits)
xor rdx, rdx        ; Clear high 64 bits of dividend
mov rbx, 7          ; Divisor
div rbx             ; RAX = quotient, RDX = remainder
```

**`idiv` (Signed Division):**
```assembly
mov rax, -100       ; Dividend
cqo                 ; Sign-extend RAX into RDX:RAX
mov rbx, 7          ; Divisor
idiv rbx            ; RAX = quotient, RDX = remainder
```

### Register Usage Patterns

**Function Parameter Registers (System V ABI):**
- **RDI:** First parameter (m in our example)
- **RSI:** Second parameter (x in our example)
- **RDX:** Third parameter (b in our example)
- **RCX:** Fourth parameter
- **R8:** Fifth parameter
- **R9:** Sixth parameter

**Return Value:**
- **RAX:** Primary return value register

### Practical Examples

**Example 1: Simple Multiplication**
```assembly
; Multiply two registers and store result
mov rax, rdi        ; Load first operand
imul rax, rsi       ; Multiply by second operand
; Result now in rax
```

**Example 2: Complex Formula - Quadratic**
```assembly
; f(x) = ax² + bx + c
; a = rdi, x = rsi, b = rdx, c = rcx
mov rax, rsi        ; rax = x
imul rax, rax       ; rax = x²
imul rax, rdi       ; rax = ax²
mov r8, rsi         ; r8 = x
imul r8, rdx        ; r8 = bx
add rax, r8         ; rax = ax² + bx
add rax, rcx        ; rax = ax² + bx + c
```

**Example 3: Using Immediate Values**
```assembly
; Multiply by constant: result = input * 10 + 5
imul rax, rdi, 10   ; rax = rdi * 10
add rax, 5          ; rax = rdi * 10 + 5
```

### Common Pitfalls and Tips

**1. Register Clobbering:**
- `mul` and single-operand `imul` modify RDX
- Save important values before multiplication

**2. Overflow Handling:**
- Multiplication can overflow - check flags if needed
- Use 64-bit registers for intermediate calculations

**3. Signed vs Unsigned:**
- Use `imul` for signed arithmetic
- Use `mul` only when you need the full 128-bit result

**4. Efficiency:**
- Three-operand `imul` is often most efficient
- Avoid unnecessary `mov` instructions

*[To be expanded with more examples from class]*

---

## 🛡 Defensive Mechanisms

### Modern Protection Mechanisms

**ASLR (Address Space Layout Randomization):**
- Randomizes memory layout
- **Bypass techniques:** Information leaks, brute force, partial overwrites

**DEP/NX (Data Execution Prevention):**
- Marks data pages as non-executable
- **Bypass techniques:** ROP, JOP, ret2libc

**Stack Canaries:**
- Random values placed before return address
- **Bypass techniques:** Canary leaks, partial overwrites

**FORTIFY_SOURCE:**
- Compile-time and runtime buffer overflow detection
- **Bypass techniques:** Heap overflows, format string bugs

*[To be expanded with class content]*

---

## 🔍 Debugging and Analysis Tools

### Essential Tools

**GDB (GNU Debugger):**
- `info registers` - Show register values
- `x/20x $rsp` - Examine stack memory
- `disas function` - Disassemble function

**Pwntools (Python):**
```python
from pwn import *
p = process('./vulnerable_binary')
payload = b'A' * 72 + p64(0xdeadbeef)
p.sendline(payload)
```

*[To be expanded with tool usage from class]*

---

## 📝 Practical Examples

*[This section will be populated with specific examples, exercises, and case studies from class]*

### Example 1: Basic Buffer Overflow
*[To be filled]*

### Example 2: ROP Chain Construction
*[To be filled]*

### Example 3: Shellcode Injection
*[To be filled]*

---

## 📖 Quick Reference

### Common x64 Instructions

**Data Movement:**
- `mov dst, src` - Move data

**Arithmetic:**
- `add dst, src` - Addition
- `sub dst, src` - Subtraction
- `inc reg` - Increment by 1
- `dec reg` - Decrement by 1
- `imul dst, src` - Signed multiplication (two-operand)
- `imul dst, src, imm` - Signed multiplication (three-operand)
- `mul src` - Unsigned multiplication (result in RDX:RAX)
- `div src` - Unsigned division (RAX = quotient, RDX = remainder)
- `idiv src` - Signed division (RAX = quotient, RDX = remainder)

**Comparison and Control Flow:**
- `cmp op1, op2` - Compare (sets flags)
- `jmp addr` - Unconditional jump
- `je/jz addr` - Jump if equal/zero
- `jne/jnz addr` - Jump if not equal/not zero

### Register Quick Reference
- **RAX:** Accumulator, system call number, return value
- **RDI:** First function argument
- **RSI:** Second function argument
- **RDX:** Third function argument
- **RCX:** Fourth function argument (user-space)
- **R8:** Fifth function argument
- **R9:** Sixth function argument
- **RSP:** Stack pointer
- **RBP:** Base pointer (frame pointer)

### System Call Quick Reference (Most Common)

| Number | Name | Purpose | Arguments |
|--------|------|---------|-----------|
| 0 | read | Read from file | fd, buf, count |
| 1 | write | Write to file | fd, buf, count |
| 2 | open | Open file | filename, flags, mode |
| 3 | close | Close file | fd |
| 9 | mmap | Map memory | addr, length, prot, flags, fd, offset |
| 10 | mprotect | Change memory protection | start, len, prot |
| 11 | munmap | Unmap memory | addr, len |
| 41 | socket | Create socket | domain, type, protocol |
| 42 | connect | Connect socket | sockfd, addr, addrlen |
| 57 | fork | Create process | - |
| 59 | execve | Execute program | filename, argv, envp |
| 60 | exit | Exit process | error_code |

### Memory Protection Flags (mprotect)
- **PROT_READ (1):** Page can be read
- **PROT_WRITE (2):** Page can be written
- **PROT_EXEC (4):** Page can be executed
- **PROT_NONE (0):** Page cannot be accessed

---

## 🎯 Exploitation Cheat Sheet

### Buffer Overflow Pattern
1. **Find vulnerability** (strcpy, gets, scanf, etc.)
2. **Determine offset** to return address
3. **Identify bad characters** (null bytes, newlines, etc.)
4. **Choose exploitation method:**
   - Direct shellcode injection
   - Return-to-libc
   - ROP chain
   - ret2syscall

### ROP Gadget Types
- **Pop gadgets:** `pop rdi; ret` - Load values into registers
- **Arithmetic gadgets:** `add rax, rbx; ret` - Perform calculations
- **Memory gadgets:** `mov [rax], rbx; ret` - Write to memory
- **System call gadgets:** `syscall; ret` - Invoke system calls

### Shellcode Encoding Techniques
- **XOR encoding:** Avoid null bytes
- **Alpha-numeric encoding:** Only printable characters
- **Polymorphic encoding:** Change appearance while maintaining functionality

---

**Last Updated:** [Date will be updated as content is added]
**Course:** CIS 5510 - Computer and Network Security
**Semester:** [To be filled]

---

## 📋 Notes Section

*[Use this space for handwritten notes during class, additional examples, and personal observations]*