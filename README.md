# Remote Environment Connection Guide - pwnnsylvania.com

This guide covers all methods for connecting to and working with the remote Linux environment hosted at pwnnsylvania.com.

## 🔑 Prerequisites & Requirements

### Network Connection
**⚠️ CRITICAL:** You must be connected to Penn's VPN to access this environment.

- **Without Penn VPN:** All connection attempts will timeout
- **With Penn VPN:** Full access to SSH, SCP, and SFTP services
- **Testing connectivity:** Use `ping pwnnsylvania.com` - should get responses when properly connected

### Connection Details
- **Host:** pwnnsylvania.com
- **Port:** 2220
- **Username:** hacker
- **Authentication:** SSH key (file: `key`)
- **Remote home directory:** `/home/<USER>

## ⚡ Quick Connection Shortcuts

**Note:** These shortcuts require adding the SSH configuration to your `~/.ssh/config` file (see SSH Config Setup below).

### Terminal SSH Connection
```bash
ssh p
```

### File Transfer with Shortcuts
```bash
# SCP Upload
scp file.txt p:/home/<USER>/

# SCP Download
scp p:/home/<USER>/file.txt ./

# SFTP
sftp p
```

### VSCode Remote Connection
1. Press `Cmd+Shift+P` / `Ctrl+Shift+P`
2. Type "Remote-SSH: Connect to Host..."
3. Select "p" from the list

---

## 🚀 Connection Methods

### 1. Terminal SSH Connection

#### Quick Connection (using provided script)
```bash
./ssh_simple.sh
```

#### Manual SSH Connection
```bash
ssh -<NAME_EMAIL> -p 2220
```

#### SSH with Verbose Output (for troubleshooting)
```bash
ssh -v -<NAME_EMAIL> -p 2220
```

### 2. VSCode Remote Development

VSCode provides an excellent way to develop directly on the remote environment with full IDE features.

#### Setup Process

**Step 1: Install Remote-SSH Extension**
1. Open VSCode
2. Go to Extensions (`Cmd+Shift+X` on macOS, `Ctrl+Shift+X` on Windows/Linux)
3. Search for "Remote - SSH" by Microsoft
4. Install the extension

**Step 2: Configure SSH Connection**

**Method A: Using Command Palette**
1. Press `Cmd+Shift+P` (macOS) or `Ctrl+Shift+P` (Windows/Linux)
2. Type "Remote-SSH: Connect to Host..."
3. Select "Add New SSH Host..."
4. Enter: `ssh -<NAME_EMAIL> -p 2220`
5. Choose your SSH config file (usually `~/.ssh/config`)

**Method B: Manual SSH Config**

Add this configuration to your `~/.ssh/config` file:
```ssh-config
Host pwnnsylvania
    HostName pwnnsylvania.com
    Port 2220
    User hacker
    IdentityFile /absolute/path/to/your/key
    IdentitiesOnly yes
```

Replace `/absolute/path/to/your/key` with the full path to your key file.

**Step 3: Connect via VSCode**
1. Press `Cmd+Shift+P` / `Ctrl+Shift+P`
2. Type "Remote-SSH: Connect to Host..."
3. Select "pwnnsylvania" from the list
4. VSCode will open a new window connected to the remote environment
5. Open folder: `/home/<USER>

#### VSCode Remote Features
- Full file editing with syntax highlighting
- Integrated terminal running on the remote system
- Extension support (install extensions on the remote)
- File explorer for the remote filesystem
- Git integration (if repositories exist on remote)
- Debugging capabilities for remote code

#### VSCode Troubleshooting
- **Connection fails:** Ensure Penn VPN is active
- **Key permission errors:** Run `chmod 600 key` on your local machine
- **"Could not establish connection":** Try connecting via terminal SSH first
- **Extensions not working:** Install them specifically for the remote environment
- **"TCP port forwarding appears to be disabled":** The server has disabled port forwarding required for VSCode Remote. Use terminal SSH instead.

## 🔧 SSH Config Setup

To use the quick connection shortcuts above, add this configuration to your `~/.ssh/config` file:

```ssh-config
# Pwnnsylvania Remote Environment
Host p
    HostName pwnnsylvania.com
    Port 2220
    User hacker
    IdentityFile /Users/<USER>/Repos/CIS5510/key
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
```

### Adding the Configuration

**Method 1: Manual Edit**
1. Open `~/.ssh/config` in your text editor
2. Add the configuration above to the end of the file
3. Save the file

**Method 2: Command Line**
```bash
cat >> ~/.ssh/config << 'EOF'

# Pwnnsylvania Remote Environment
Host p
    HostName pwnnsylvania.com
    Port 2220
    User hacker
    IdentityFile /Users/<USER>/Repos/CIS5510/key
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
EOF
```

### Configuration Explanation
- **Host p:** Creates the shortcut alias
- **IdentityFile:** Points to your private key (update path if different)
- **IdentitiesOnly yes:** Security setting to only use specified key
- **ServerAliveInterval 60:** Sends keepalive every 60 seconds
- **ServerAliveCountMax 3:** Tries 3 times before declaring connection dead

---

## 📁 File Transfer Methods

### SCP (Secure Copy Protocol)

**Important:** SCP uses `-P` (uppercase) for port, while SSH uses `-p` (lowercase).

#### Basic Syntax
```bash
scp -i key -P 2220 [source] [destination]
```

#### Upload Examples
```bash
# Upload single file
scp -i key -P 2220 localfile.txt <EMAIL>:/home/<USER>/

# Upload multiple files
scp -i key -P 2220 file1.txt file2.py <EMAIL>:/home/<USER>/

# Upload entire directory (recursive)
scp -i key -P 2220 -r project_folder/ <EMAIL>:/home/<USER>/

# Upload to specific remote directory
scp -i key -P 2220 solution.py <EMAIL>:/home/<USER>/assignments/

# Upload with wildcards
scp -i key -P 2220 *.txt <EMAIL>:/home/<USER>/
```

#### Download Examples
```bash
# Download single file
scp -i key -P 2220 <EMAIL>:/home/<USER>/result.txt ./

# Download to specific local directory
scp -i key -P 2220 <EMAIL>:/home/<USER>/flag.txt ./downloads/

# Download entire directory
scp -i key -P 2220 -r <EMAIL>:/home/<USER>/results ./

# Download multiple files
scp -i key -P 2220 <EMAIL>:/home/<USER>/file1.txt <EMAIL>:/home/<USER>/file2.txt ./

# Download with wildcards
scp -i key -P 2220 <EMAIL>:/home/<USER>/*.log ./
```

### SFTP (Interactive File Transfer)

#### Start SFTP Session
```bash
sftp -i key -P 2220 <EMAIL>
```

#### Common SFTP Commands
```bash
# File transfer operations
put localfile.txt                    # Upload file to current remote directory
put localfile.txt remotename.txt     # Upload and rename file
get remotefile.txt                   # Download file to current local directory
get remotefile.txt localname.txt     # Download and rename file
put -r local_folder/                 # Upload directory recursively
get -r remote_folder/                # Download directory recursively

# Navigation commands
ls                                   # List remote files and directories
lls                                  # List local files and directories
cd remote_directory                  # Change remote directory
lcd local_directory                  # Change local directory
pwd                                  # Show current remote directory
lpwd                                 # Show current local directory

# Directory operations
mkdir new_folder                     # Create remote directory
lmkdir new_folder                    # Create local directory
rmdir old_folder                     # Remove remote directory

# Utility commands
help                                 # Show available commands
exit                                 # Close SFTP session
quit                                 # Alternative to exit
```

### VSCode File Transfer
When using VSCode Remote-SSH:
- Files are automatically synchronized
- Edit files directly in VSCode - changes are saved remotely
- Use the integrated terminal for file operations
- Drag & drop files in the VSCode explorer (limited support)

## 📋 Example Workflows

### Workflow 1: Upload Assignment Solution
```bash
# Upload your Python solution
scp -i key -P 2220 assignment1.py <EMAIL>:/home/<USER>/

# Or upload multiple related files
scp -i key -P 2220 assignment1.py data.txt readme.md <EMAIL>:/home/<USER>/

# Using shortcuts (after SSH config setup)
scp assignment1.py p:/home/<USER>/
```

### Workflow 2: Download Results and Flags
```bash
# Download flag or results
scp -i key -P 2220 <EMAIL>:/home/<USER>/flag.txt ./

# Download entire results directory
scp -i key -P 2220 -r <EMAIL>:/home/<USER>/results ./

# Using shortcuts
scp p:/home/<USER>/flag.txt ./
```

### Workflow 3: Interactive File Management with SFTP
```bash
# Start SFTP session
sftp -i key -P 2220 <EMAIL>

# Once connected, you can:
sftp> ls                          # See remote files
sftp> lls                         # See local files
sftp> put script.py               # Upload script.py
sftp> get output.txt              # Download output.txt
sftp> cd assignments              # Change to assignments directory
sftp> put -r project_folder/      # Upload entire folder
sftp> exit                        # Close session
```

## 🛠 Troubleshooting

### Connection Issues

**Problem:** Connection timeouts or "Operation timed out"
- **Solution:** Ensure you're connected to Penn's VPN
- **Test:** Try `ping pwnnsylvania.com` - should get responses

**Problem:** "Permission denied (publickey)"
- **Solution:** Check SSH key permissions: `chmod 600 key`
- **Test:** Verify key format: `file key` should show "OpenSSH private key"

**Problem:** "Connection refused"
- **Solution:** Verify you're using the correct port (2220)
- **Test:** Try `nc -v pwnnsylvania.com 2220` to test port connectivity

### File Transfer Issues

**Problem:** SCP/SFTP connection fails
- **Solution:** Remember to use `-P 2220` (uppercase P) for port
- **Test:** Try connecting via SSH first to verify connectivity

**Problem:** File not found on remote
- **Solution:** Connect via SSH and verify the file path exists
- **Tip:** Use `ls -la` to check file permissions and existence

### Advanced Troubleshooting

**Using verbose mode for debugging:**
```bash
# SSH with verbose output
ssh -v -<NAME_EMAIL> -p 2220

# SCP with verbose output
scp -v -i key -P 2220 file.txt <EMAIL>:/home/<USER>/

# SFTP with verbose output
sftp -v -i key -P 2220 <EMAIL>
```

**Testing connectivity:**
```bash
# Test basic connectivity
ping pwnnsylvania.com

# Test port connectivity
nc -v pwnnsylvania.com 2220

# Test SSH key format
file key    # Should show "OpenSSH private key"
```

### VSCode Remote Issues

**Problem:** "Could not establish connection to server"
- **Solution:** Test SSH connection in terminal first
- **Check:** Verify Penn VPN is active and SSH config is correct

**Problem:** "TCP port forwarding appears to be disabled"
- **Cause:** The remote server has disabled TCP port forwarding in SSH configuration
- **Impact:** VSCode Remote-SSH cannot function without port forwarding
- **Solution:** Use terminal SSH instead: `ssh p` or `./ssh_simple.sh`
- **Alternative:** Edit files locally and transfer via SCP

**Problem:** Remote extensions not working
- **Solution:** Install extensions specifically on the remote environment
- **Tip:** Look for "Install on SSH: pwnnsylvania" option

## 💡 Best Practices & Tips

### Connection Management
1. **Always connect to Penn VPN first** before attempting any connections
2. **Test basic SSH connectivity** before trying advanced features
3. **Use the provided script** (`./ssh_simple.sh`) for quick terminal access
4. **Keep your SSH key secure** with proper permissions (600)

### File Management
1. **Use VSCode Remote** for development work - it's the most efficient
2. **Use SCP for batch transfers** of multiple files or directories
3. **Use SFTP for interactive management** when you need to browse and organize
4. **Remember:** `/home/<USER>'t
5. **Use wildcards** for pattern matching: `*.txt`, `assignment*.py`, etc.
6. **Port differences:** SSH uses `-p 2220` (lowercase), SCP/SFTP use `-P 2220` (uppercase)

### Development Workflow
1. **Connect via VSCode Remote** for primary development
2. **Use integrated terminal** for running commands and tests
3. **Edit files directly in VSCode** - changes are automatically saved remotely
4. **Download results** via SCP when needed locally

### Security Notes
- SSH key should have 600 permissions (`chmod 600 key`)
- Never share your private key file
- The key provides full access to the remote environment
- Files in `/home/<USER>

## 📚 Additional Resources

- **Detailed file transfer guide:** See `./guides/ssh_scp_guide.md`
- **SSH connection script:** `./ssh_simple.sh`
- **SSH key files:** `key` (private), `key.pub` (public)

---

**Quick Reference:**
- SSH: `ssh p`
- SCP Upload: `scp file.txt p:/home/<USER>/`
- SCP Download: `scp p:/home/<USER>/file.txt ./`
- SFTP: `sftp p`
- Manual SSH: `ssh -<NAME_EMAIL> -p 2220`
